const plugin = require('tailwindcss/plugin');
function generateNumUnits(from, to, step = 1) {
	/*
    样式中避免单位给0
    */
	const ret = {};
	for (let i = from; i <= to; i += step) {
		ret[i] = i ? (i / 75) * 20 + 'vw' : i;
	}
	return ret;
}
/** @type {import('tailwindcss').Config} */
module.exports = {
	content: ['./src/**/*.{vue,js,ts,jsx,tsx}'],
	theme: {
		spacing: {
			1: '1px',
			...generateNumUnits(0, 1000, 1)
		},
		fontSize: generateNumUnits(10, 80, 1),
		lineHeight: generateNumUnits(20, 100),
		colors: {
			main: 'hsla(222, 9%, 23%, 1)',
			white: 'hsla(0, 0%, 100%, 1)',
			gray: {
				20: 'hsla(0, 0%, 20%, 1)',
				27: 'hsla(0, 0%, 27%, 1)',
				40: 'hsla(0, 0%, 40%, 1)',
				58: 'hsla(0, 0%, 58%, 1)',
				60: 'hsla(0, 0%, 60%, 1)',
				63: 'hsla(0, 0%, 63%, 1)',
				80: 'hsla(0, 0%, 80%, 1)',
				90: 'hsla(0, 0%, 90%, 1)',
				95: 'hsla(0, 0%, 95%, 1)',
				100: 'hsla(0, 0%, 100%, 1)',
				dialog: 'hsla(0, 0%, 0%, 0.6)'
			}
		},
		extend: {
			borderRadius: generateNumUnits(2, 60),
			flex: {
				100: '1 1 100%'
			}
		}
	},
	variants: {
		extend: {}
	},
	plugins: [
		plugin(function ({ addUtilities, matchUtilities }) {
			addUtilities({
				'.center': {
					display: 'flex',
					'align-items': 'center',
					'justify-content': 'center'
				}
			});
			matchUtilities({
				words: value => ({
					'max-width': value,
					overflow: 'hidden',
					'text-overflow': 'ellipsis',
					'white-space': 'nowrap'
				})
			});
		})
	]
};
