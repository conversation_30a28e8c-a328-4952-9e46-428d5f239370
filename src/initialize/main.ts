import { createApp } from 'vue';
import '../assets/style';
import '../assets/style/tailMain.css';
import './protocol';

const SetBaseWidth = () => {
  const clientWidth = document.documentElement.clientWidth;
  const baseFontSize = 16 * (Math.min(clientWidth, 480) / 375);
  document.documentElement.style.fontSize = baseFontSize + 'px';
};

window.addEventListener('resize', function () {
  SetBaseWidth();
});

export function InitApp(App) {
  const app = createApp(App);
  app.mount('#app');
}
