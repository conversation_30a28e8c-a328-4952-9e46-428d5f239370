import MCProtocol from '@simplex/simple-mcprotocol';

// 获取ios基础参数协议
MCProtocol.register('jiakao-global.getStatistics', config => {
  return {
    callback: config.callback
  };
});

// 是否第一次进入
MCProtocol.register('jxxy.enterFirstEnquiryH5', config => {
  return {
    callback: config.callback
  };
});

// 检查是否有精准定位权限
MCProtocol.register('permission.permission.check', config => {
  return {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    config: config.config,
    callback: config.callback
  };
});

// 弹窗精准定位授权弹窗
MCProtocol.register('jxxy.show.location', config => {
  return {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    manual: config.manual,
    callback: config.callback
  };
});

// 获取定位策略
MCProtocol.register('jiakao-global.getAbTestConfig', config => {
  return {
    callback: config.callback
  };
});

// 阿里云手机号码验证服务
MCProtocol.register('Core.User.numAuth', config => {
  return {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    phoneNum: config.phone,
    callback: config.callback
  };
});

// 获取位置信息
MCProtocol.register('Core.System.locationInfo', config => {
  return {
    callback: config.callback
  };
});

// 留资完成
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
MCProtocol.register('jxxy.enquiry.finished', () => {
  return {};
});

// 分布提交
MCProtocol.register('jxxy.enquiry.saveOrderNo', config => {
  return {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    orderNo: config.orderNo,
    callback: config.callback
  };
});

// 留资失败把数据给客户端缓存
MCProtocol.register('jiakao-global.saveErrorReq', config => {
  return {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    reqConfig: config.reqConfig,
    callback: config.callback
  };
});
