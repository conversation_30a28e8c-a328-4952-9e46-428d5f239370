import { request } from '@/utils/store';
import { MethodEnum } from '@/utils/store/type';

// 分步留资
export const PostAddPartStore = (params, baseParams = '') => {
  let url = `${APP.domain.lead}/api/open/lead/add-part.htm`;

  if (baseParams) {
    url = `${url}?${baseParams}`;
  }

  return request({
    url,
    method: MethodEnum.POST,
    params,
    options: {
      sign: '*#06#a4qjhYuLjpVHjXZtjKOJh4Y9'
    }
  });
};

// 机动车留资
export const PostAdd2Store = (params, baseParams = '') => {
  let url = `${APP.domain.lead}/api/open/lead/add.htm`;

  if (baseParams) {
    url = `${url}?${baseParams}`;
  }

  return request({
    url,
    method: MethodEnum.POST,
    params,
    options: {
      sign: '*#06#a4qjhYuLjpVHjXZtjKOJh4Y9'
    }
  });
};

// 无人机留资
export const PostAddUavStore = (params, baseParams = '') => {
  let url = `${APP.domain.lead}/api/open/lead/uav-add.htm`;

  if (baseParams) {
    url = `${url}?${baseParams}`;
  }

  return request({
    url,
    method: MethodEnum.POST,
    params,
    options: {
      sign: '*#06#a4qjhYuLjpVHjXZtjKOJh4Y9'
    }
  });
};
