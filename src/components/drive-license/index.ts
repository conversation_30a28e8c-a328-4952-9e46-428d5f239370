import MCProtocol from '@simplex/simple-mcprotocol';
import Utils from '@/utils/utils';
import { trackEvent } from '@/utils/stat';
import { defineComponent, onMounted, reactive, toRefs } from 'vue';
import { CAR_DESC, CAR_TYPE } from '@/utils/storage-key';
import { StateModel } from './types';
import { LicenseTypeEnum } from '@/components/drive-license/constants';
import { LICENSE_OPTIONS, UAV_LICENSE_LIST, UAV_LICENSE_OPTIONS } from '@/utils/options/license';
import { GroupItemModel, ItemModel } from '@/utils/types/license';

export default defineComponent({
  setup() {
    const state = reactive<StateModel>({
      activeTab: LicenseTypeEnum.CAR,
      // 机动车驾照类型
      carType: '',
      // 无人机驾照类型
      uavType: ''
    });

    const constants = {
      LicenseTypeEnum,
      LICENSE_OPTIONS,
      UAV_LICENSE_OPTIONS
    };

    const methods = {
      onSwitch(type: LicenseTypeEnum) {
        if (state.activeTab === type) {
          return;
        }

        state.activeTab = type;
      },
      // 机动车选中
      onClick(value: ItemModel) {
        const { carType, carDesc } = value;

        state.carType = carType;
        Utils.store.set(CAR_TYPE, carType);
        Utils.store.set(CAR_DESC, carDesc);

        trackEvent({
          PageName: '帮我找驾校h5-驾照类型页',
          actionName: '帮我找驾校驾照类型页_点击按钮',
          actionType: '',
          numbers: {
            strs: {
              buttonName: carType
            }
          }
        });

        MCProtocol.Core.Web.close();
      },
      // 无人机选中
      onClickUav(item: GroupItemModel, value: ItemModel) {
        const { groupName } = item;
        const { carType, carDesc } = value;

        state.uavType = carType;
        Utils.store.set(CAR_TYPE, carType);
        Utils.store.set(CAR_DESC, `${groupName}（${carDesc}）`);

        trackEvent({
          PageName: '帮我找驾校h5-驾照类型页',
          actionName: '帮我找驾校驾照类型页_点击按钮',
          actionType: '',
          numbers: {
            strs: {
              buttonName: carType
            }
          }
        });

        MCProtocol.Core.Web.close();
      },
      formatDefault() {
        const carType = Utils.store.get(CAR_TYPE);

        if (!carType) {
          state.activeTab = LicenseTypeEnum.CAR;
          state.carType = 'C1';
          state.uavType = '';

          return;
        }

        if (UAV_LICENSE_LIST.includes(carType)) {
          state.activeTab = LicenseTypeEnum.UAV;
          state.carType = '';
          state.uavType = carType;

          return;
        }

        state.activeTab = LicenseTypeEnum.CAR;
        state.carType = carType;
        state.uavType = '';
      }
    };

    onMounted(() => {
      methods.formatDefault();

      trackEvent({
        PageName: '帮我找驾校h5-驾照类型页',
        actionName: '加载成功',
        actionType: ''
      });
    });

    return {
      ...toRefs(state),
      ...constants,
      ...methods
    };
  }
});
