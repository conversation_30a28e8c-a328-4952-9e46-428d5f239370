<template>
  <div class="question-list">
    <div class="content" v-if="list?.length">
      <div class="list-item" v-for="(item, index) in list" :key="index">
        <p>{{ item.content }}</p>
        <ul>
          <li
            :class="selectList[index].optionIndexList.includes(option.index) ? 'active' : ''"
            v-for="option in item.optionList"
            :key="option.index"
            @click="onSelectOption(index, option)"
          >
            {{ option.value }}
          </li>
        </ul>
      </div>
    </div>

    <slot name="footer"></slot>
  </div>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
