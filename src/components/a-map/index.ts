import MCProtocol from '@simplex/simple-mcprotocol';
import Utils from '@/utils/utils';
import { trackEvent } from '@/utils/stat';
import { defineComponent, reactive, toRefs, ref, onUnmounted, watch } from 'vue';
import { StateModel } from './types';
import AMapInit from '@/utils/a-map';
import SelectCityComp from '@/components/select-city/index.vue';
import { MAP_PLUGINS_LIST, MapStatusCodeEnum } from './constant';
import { ADDRESS, HANDLE_CHOOSE_LAT_BOOL } from '@/utils/storage-key';
import { ALL_CITY } from '@/components/select-city/constants';
import { ACCESS_FINE_LOCATION_STATUS } from '@/utils/constant';
import { useLocationStore } from '@/utils/hooks/location';

export default defineComponent({
  // 组件
  components: {
    SelectCityComp
  },
  setup() {
    // 地图容器
    const mapContainer = ref<HTMLElement>(null);
    // 地图类
    let AMapObj: any = null;
    // 地图实例
    let mapInstance: any = null;
    // 搜索实例
    let placeSearch: any = null;

    const locationStore = useLocationStore();

    let userCity = '';

    const state = reactive(<StateModel>{
      // 搜索的城市
      searchKeywords: '',
      // 搜索的结果
      mapSearchList: [],
      // 默认北京
      city: {
        // code: '110000',
        // name: '北京市'
      },
      // 默认北京坐标
      location: {
        // longitude: 116.407387,
        // latitude: 39.904179
      },
      //取消按钮是否展示
      cancelVisible: false,
      // 顶部padding，默认iphone13
      top: 47,
      userCityName: '',
      // 获取地图权限按钮
      handleToLocation: false
    });
    // 事件
    const methods = {
      // 初始化
      async init() {
        trackEvent({
          PageName: '帮我找驾校h5-地图页',
          actionName: '加载成功',
          actionType: ''
        });
        // 初始化组件
        await AMapInit(MAP_PLUGINS_LIST);
        // 设置padding
        methods.setTop();

        await methods.initLocation();
      },
      // 设置padding
      setTop() {
        MCProtocol.Core.System.env(res => {
          state.top = res.data.statusBarHeight + 96;
        });
      },

      async initLocation() {
        const { _userCity } = await Utils.getSystemBaseInfo();
        userCity = _userCity;
        // 安卓展示获取地图权限按钮
        if (Utils.isAndroid) {
          const hasPosition = await Utils.checkAccurateControl();
          if (hasPosition.message !== ACCESS_FINE_LOCATION_STATUS.success) {
            methods.updateCityCode(userCity);
            state.handleToLocation = true;
          }
        }
        methods.setMapConfig(AMap);
      },

      async setMapConfig(AMap) {
        AMapObj = AMap;
        const map = new AMap.Map(mapContainer.value, {
          resizeEnable: true,
          zoom: 18
        });
        map.addControl(
          new AMap.ToolBar({
            visible: true,
            position: {
              bottom: '10px',
              right: '10px'
            }
          })
        );
        map.addControl(
          new AMap.Scale({
            visible: true
          })
        );
        mapInstance = map;

        const address = Utils.store.get(ADDRESS);
        // 设置地图中心
        if (address) {
          state.location = {
            longitude: JSON.parse(address).location.lng,
            latitude: JSON.parse(address).location.lat
          };
          mapInstance.setCenter([state.location.longitude, state.location.latitude]);
          // 根据坐标获取城市名称
          methods.setGeocoder(state.location.longitude, state.location.latitude);
        } else {
          methods.updateCityCode(userCity);
        }
        // 点击切换位置`
        mapInstance.on('click', e => {
          const lng = e.lnglat.getLng(),
            lat = e.lnglat.getLat();
          const position = [lng, lat];
          mapInstance.setCenter(position);
        });
        // 地图搜索插件
        methods.placeSearchChange(state.city.code);
        // 加载拖拽手势
        mapInstance && methods.loadPicker();
      },
      // 递归获取城市名称
      getCityName(cityCode, cityData) {
        cityData.forEach(item => {
          if (cityCode === item.code) {
            state.userCityName = item.name;
          } else {
            if (item.cities && item.cities.length) {
              methods.getCityName(cityCode, item.cities);
            }
          }
        });
      },
      // 地图搜索插件
      placeSearchChange(code: string) {
        if (!AMapObj) {
          return;
        }
        AMapObj.plugin(['AMap.PlaceSearch', 'AMap.AutoComplete'], () => {
          placeSearch = new AMapObj.PlaceSearch({
            citylimit: true,
            pageSize: 50,
            city: code
          });
        });
      },
      // 监听input输入
      onSearch: Utils.debounce(() => {
        placeSearch.search(state.searchKeywords, (status, result) => {
          // 查询成功时，result即对应匹配的POI信息
          if (status === MapStatusCodeEnum.complete && result && result.poiList) {
            state.mapSearchList = result.poiList.pois;
            mapInstance.setFitView();
          }
        });
      }, 200),
      //input获得焦点
      onFocus() {
        state.cancelVisible = true;
      },
      handleLocation() {
        methods.onFocus();
      },
      async openLocation() {
        const result = await locationStore.showRequestDialog();
        if (result._latitude) {
          state.handleToLocation = false;
          const arr = [result._longitude, result._latitude];
          methods.loadPicker(arr);
        }
      },
      // 根据坐标获取城市名称
      setGeocoder(lng: number, lat: number) {
        // 获取坐标和地名互转插件
        AMapObj.plugin('AMap.Geocoder', function () {
          const geocoder = new AMapObj.Geocoder();
          geocoder.getAddress(new AMapObj.LngLat(lng, lat), function (status, result) {
            if (status === 'complete' && result.regeocode) {
              // 设置回填
              state.city = {
                code: result.regeocode.addressComponent.adcode,
                name: result.regeocode.addressComponent.city || result.regeocode.addressComponent.province
              };
            }
          });
        });
      },
      //加载移动工具
      async loadPicker(arr?: Array<number>) {
        AMapUI.loadUI(['misc/PositionPicker'], PositionPicker => {
          mapInstance.addControl(new AMapObj.Scale());
          if (arr.length > 0) {
            mapInstance.setCenter(arr);
          } else {
            methods.updateCity(userCity);
          }
          const positionPicker = new PositionPicker({
            mode: 'dragMap',
            map: mapInstance
          });
          positionPicker.on('success', function (positionResult) {
            state.mapSearchList = positionResult.regeocode.pois;
          });
          positionPicker.on('fail', function () {
            state.mapSearchList = [];
          });
          positionPicker.start();
        });
      },
      // 打开城市选择
      openSelectCity() {
        components.selectCityCompRef.value.onShow();
      },
      // 城市组件emit
      updateCityCode(cityCode: string) {
        state.userCityName = '';
        methods.getCityName(cityCode, ALL_CITY);
        state.city = { name: state.userCityName, code: cityCode };
        methods.updateCity(cityCode);
      },
      // 更新城市
      updateCity(code) {
        if (!mapInstance) {
          return;
        }
        mapInstance.setCity(code, () => {
          mapInstance.setZoom(16);
        });
      },
      // 取消
      onCancel() {
        state.cancelVisible = false;
      },
      // 跳转到首页
      async goIndex(item) {
        Utils.store.set(HANDLE_CHOOSE_LAT_BOOL, true);
        if (item.location.lng && item.location.lat) {
          const AMap = await AMapInit(['AMap.Geocoder']);
          const geocoder = new AMap.Geocoder({
            city: state.city.code
          });
          const position = [item.location.lng, item.location.lat];
          geocoder.getAddress(position, (status, result) => {
            if (status === 'complete' && result.info === 'OK') {
              const address = {
                cityCode: result.regeocode.addressComponent.adcode,
                name: item.address,
                location: {
                  lng: position[0],
                  lat: position[1],
                  adcode: result.regeocode.addressComponent.adcode,
                  province: result.regeocode.addressComponent.province,
                  city: result.regeocode.addressComponent.city,
                  district: result.regeocode.addressComponent.district
                }
              };
              Utils.store.set(ADDRESS, JSON.stringify(address));
            }
            MCProtocol.Core.Web.close();
          });
        }
        return;
      }
    };
    // 监听
    watch(
      () => state.city.code,
      val => {
        methods.placeSearchChange(val);
      }
    );
    onUnmounted(() => {
      mapInstance && mapInstance.destroy();
      placeSearch = null;
      mapInstance = null;
      AMapObj = null;
    });

    methods.init();
    // 组件集合
    const components = {
      selectCityCompRef: ref<InstanceType<typeof SelectCityComp>>(null),
      mapContainer
    };
    return {
      ...methods,
      ...components,
      ...toRefs(state)
    };
  }
});
