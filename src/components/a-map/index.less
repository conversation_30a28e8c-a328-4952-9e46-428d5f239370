// 搜索框背景透明
.bg-transparent {
  background-color: transparent;
}
// 地图高度
.map-container-box {
  height: calc(100vh - 150px);
}
// 城市名称
.icon {
  width: 80px;
  max-width: 200px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  background-image: url('https://web-resource.mc-cdn.cn/web/2x7hocxON/7b305744-6f83-466f-9dec-15dcdb2cf109');
  background-repeat: no-repeat;
  background-position: right center;
  background-size: 8px 6px;
  padding-right: 12px;
}
// 分隔线
.search-split {
  width: 1px;
  height: 15px;
  background-color: #ccc;
}
// 列表图标
.active {
  width: 16px;
  height: 22px;
  background: url('https://web-resource.mc-cdn.cn/web/2x7hocxON/1d091c3e-7d1b-48a5-8871-91a4958e572d') no-repeat center;
  background-size: 100% auto;
}
.no-active {
  width: 16px;
  height: 18px;
  background: url('https://web-resource.mc-cdn.cn/web/2x7hocxON/e08f8043-051c-483b-b22f-8b3ddf9fac59!150x0') no-repeat
    center;
  background-size: 100% auto;
}

// 聚焦样式
.cancel-visible {
  position: fixed;
  z-index: 200;
  bottom: 0;
  background-color: #fff;
  left: 0;
  right: 0;
  height: auto;
  transition: all 0.1 ease-in-out;
}

.empty-location {
  position: relative;
  width: 100%;
  height: 80vh;
  display: flex;
  justify-content: center;
  align-items: center;

  .empty-location-item {
    margin: -160px 30px 0 30px;
    width: 100%;

    .empty-location-icon {
      width: 170px;
      height: 110px;
      background: url('./images/<EMAIL>') no-repeat center;
      background-size: 100% auto;
      margin: 0 auto;
    }
    p {
      margin-top: 6px;
      font-size: 13px;
      font-family: PingFang SC, PingFang SC-Regular;
      font-weight: Regular;
      text-align: center;
      color: #999999;
      line-height: 18px;
    }
    .btn-list {
      display: flex;
      justify-content: center;
      margin-top: 40px;
      .empty-location-btn {
        width: 125px;
        height: 40px;
        font-size: 16px;
        border: 1px solid #1dacf9;
        font-weight: 500;
        text-align: center;
        color: #ffffff;
        line-height: 40px;
        border-radius: 4px;
      }
      .btn-list-left {
        background: #fff;
        margin-right: 20px;
        color: #1dacf9;
      }
      .btn-list-right {
        background: linear-gradient(90deg, #00e0e5 3%, #0086fa 95%);
        color: #ffffff;
      }
    }
  }
}
