export interface StateModel {
  // 搜索框输入值
  searchKeywords: string;
  // 地址选择列表
  mapSearchList: Array<MapSearchListModel>;
  // 默认城市
  city: CityItemModel;
  // 取消按钮是否展示
  cancelVisible: boolean;
  // 顶部padding，默认iphone13
  top: number;
  // 缓存的地址数据
  location: MapLocationModel;
  userCityName: string;
  handleToLocation: boolean;
}
// emit抛出类型
export interface EmitModel {
  (e: 'updateCarType', type: string);
}
/**
 * 地图定位信息
 */
export interface MapLocationModel {
  longitude: number;
  latitude: number;
  address?: string;
}
interface mapSearchListLocationModel {
  KL: number;
  className: string;
  kT: number;
  lat: number;
  lng: number;
  pos: Array<string>;
}
//列表搜索信息
export interface MapSearchListModel {
  address: string;
  businessArea: string;
  direction: string;
  distance: number;
  id: string;
  name: string;
  tel: string;
  type: string;
  location: Array<mapSearchListLocationModel>;
}

// 城市组件通讯类型
export interface CityItemModel {
  code: string;
  name: string;
  pinyin?: string;
}
