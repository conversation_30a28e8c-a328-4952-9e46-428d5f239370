<template>
  <div class="com-select" v-if="show">
    <div class="select-city-box select-city">
      <div @click="onHide" class="w-25 h-25 icon mr-32 ml-15 mt-10"></div>
      <div id="hot" class="hot-wrap">
        <div class="hot">热门城市</div>
        <div class="hot-r">
          <div class="hot-item" v-for="(item, index) in hotCity" :key="index">
            <div class="hotTxt" @click="selectCity(item)" hover="true" hover-class="hover-hot-i">
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
      <div :id="ki(item)" class="zimu-wrap" v-for="(item, index) in shouzimuList" :key="index">
        <div class="zimu-group">{{ item }}</div>
        <div v-for="($value, index) in shouzimuMaps[`${item}`]" :key="index">
          <div class="zimu-queen">
            <span @click="selectCity($value)">{{ $value.name }}</span>
          </div>
        </div>
      </div>

      <div class="zimu-nav">
        <div data-id="hot">#</div>
        <div v-for="(item, index) in shouzimuList" :key="index">
          <div @click="gotoLetter(item)">{{ item }}</div>
        </div>
      </div>
    </div>
  </div>
  <!-- 开启底部安全区适配 -->
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
