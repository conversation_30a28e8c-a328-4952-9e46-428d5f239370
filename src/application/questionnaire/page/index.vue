<template>
  <div class="question-container">
    <header-comp title="问卷调查" @onBack="onBack" />

    <div class="top">
      <div class="top-title">
        <span>您的学车需求已提交</span>
      </div>
      <p>驾校或教练稍后将会联系您，请保持电话畅通</p>
    </div>

    <question-list-comp :list="questionList" @change="onQuestionSelect">
      <template #footer>
        <div class="footer">
          <ul>
            <li @click="onSkip">跳过</li>
            <li class="active" @click="onComplete">完成</li>
          </ul>
        </div>
      </template>
    </question-list-comp>

    <div class="tips" v-if="remoteSettingStore.state.questionnaireTipVisible">
      <p>如您需要取消咨询，可通过下述操作方式进行取消</p>
      <p>我的-我的订单-咨询驾校/教练订单</p>
    </div>

    <dialog-result-comp ref="dialogResultRef" />
  </div>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
