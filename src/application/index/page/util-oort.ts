import { trackEvent } from '@/utils/stat';
import { UrlQueryModel } from '@/application/index/page/comp/form/types';
import { DEFAULT_CONTENT } from '@/application/index/page/comp/dialog-double-confirm/constants';
import { ToastEnum } from '@/application/index/page/comp/form/constants';

// 新版本修改上报逻辑，去掉pullSuccess，只有命中才上报

// 禁止一键登录
export const oortDisabledQuicklyLogin = () => {
  trackEvent({
    PageName: '帮我找驾校页h5',
    actionName: '禁止一键登录',
    actionType: ''
  });
};

// 留资前置加载成功
export const oortPreloadSuccess = () => {
  trackEvent({
    PageName: '帮我找驾校h5留资页',
    actionName: '加载成功',
    actionType: '',
    abTest: '留资前置'
  });
};

// 页面开始加载
export const oortPageStartLoad = (entrance: UrlQueryModel['entrance']) => {
  trackEvent({
    PageName: '帮我找驾校页h5',
    actionName: '开始加载',
    actionType: '',
    prePageName: entrance === 'newuser' ? '新手引导—是否报名驾校' : '“报名”页卡首页'
  });
};

// 关闭按钮
export const oortCloseButton = (entrance: UrlQueryModel['entrance'], phoneNumberInput: 1 | 0, nameInput: 1 | 0) => {
  trackEvent({
    PageName: '帮我找驾校h5留资页',
    actionName: '点击按钮',
    actionType: '',
    abTest: '留资前置',
    buttonName: '返回'
  });

  trackEvent({
    PageName: '帮我找驾校页h5',
    actionName: '点击“关闭”按钮',
    actionType: '',
    // 进入入口
    prePageName: entrance === 'newuser' ? '新手引导—是否报名驾校' : '“报名”页卡首页',
    numbers: {
      // 1修改过手机号并且不为空，0默认
      phoneNumberInput,
      // 1修改过称呼并且不为空，0默认
      nameInput
    }
  });
};

// 提交按钮
export const oortSubmitButton = (entrance: UrlQueryModel['entrance'], nameInput: 1 | 0) => {
  trackEvent({
    PageName: entrance === 'newuser' ? '新手引导-66学车节帮我找驾校-提交学车意向' : '66学车节帮我找驾校-免费咨询',
    actionName: '点击',
    actionType: ''
  });

  trackEvent({
    PageName: '帮我找驾校h5留资页',
    actionName: '点击按钮',
    actionType: '',
    abTest: '留资前置',
    buttonName: '提交'
  });

  trackEvent({
    PageName: '帮我找驾校页h5',
    actionName: '点击“提交学车意向”按钮',
    actionType: '',
    numbers: {
      nameInput
    }
  });
};

// 二次确认弹窗
export const oortReconfirmDialog = () => {
  trackEvent({
    PageName: '帮我找驾校页h5',
    actionName: '二次确认弹窗',
    actionType: ''
  });
};

// 二次确认弹窗展示
export const oortReconfirmDialogShow = () => {
  trackEvent({
    PageName: '帮我找驾校页h5',
    actionName: '二次确认弹窗-展示',
    actionType: '',
    numbers: {
      content: DEFAULT_CONTENT
    }
  });
};

// 页面加载成功
export const oortPageLoadSuccess = (entrance: UrlQueryModel['entrance'], addressName = '') => {
  trackEvent({
    PageName: '帮我找驾校页h5',
    actionName: '加载成功',
    actionType: '',
    prePageName: entrance === 'newuser' ? '新手引导—是否报名驾校' : '“报名”页卡首页',
    locationInput: addressName ? 1 : 0
  });
};

// 一键登录结束
export const oortQuickLoginEnd = () => {
  trackEvent({
    PageName: '帮我找驾校h5留资页',
    actionName: '一键登录后的信息',
    actionType: ''
  });
};

// 表单校验的埋点
export const oortToast = (type: ToastEnum) => {
  trackEvent({
    PageName: '帮我找驾校页h5',
    actionName: 'toast显示类型',
    actionType: '',
    numbers: {
      toastType: type
    }
  });
};

// 表单校验成功
export const oortFormCheckSuccess = (entrance: UrlQueryModel['entrance']) => {
  trackEvent({
    PageName: '帮我找驾校页h5-短信验证码弹窗',
    actionName: '展示',
    actionType: '',
    prePageName: entrance === 'newuser' ? '新手引导—是否报名驾校' : '“报名”页卡首页'
  });
};

// 阿里云手机号验证结果
export const oortAliyunPhoneCheck = (aliclouStatus: 1 | 2) => {
  trackEvent({
    PageName: '帮我找驾校页h5',
    actionName: '帮我找驾校页h5_点击提交按钮时阿里云状态',
    actionType: '',
    numbers: {
      aliclouStatus
    }
  });
};

// 已登录用户的提交
export const oortLoginUserSubmit = (entrance: UrlQueryModel['entrance']) => {
  trackEvent({
    PageName: entrance === 'newuser' ? '新手引导-66学车节帮我找驾校-已登录' : '66学车节帮我找驾校-已登录',
    actionName: '提交',
    actionType: ''
  });
};

// 短信验证码弹窗登录
export const oortSmsLogin = (entrance: UrlQueryModel['entrance']) => {
  trackEvent({
    PageName: '帮我找驾校页h5-短信验证码弹窗-提交按钮',
    actionName: '点击',
    actionType: '',
    prePageName: entrance === 'newuser' ? '新手引导—是否报名驾校' : '“报名”页卡首页'
  });
};

// 通知客户端更新定位
export const oortUpdateLocation = (orderNo: string, callBack: 1 | 0) => {
  trackEvent({
    PageName: '帮我找驾校页h5',
    actionName: '通知客户端更新定位',
    actionType: '',
    orderNo,
    callBack
  });
};

// 开始提交线索
export const oortStartSubmit = (orderNo: string, submitType: 1 | 0) => {
  trackEvent({
    PageName: '帮我找驾校页h5',
    actionName: '开始提交线索',
    actionType: '',
    orderNo,
    submitType
  });
};

// 线索提交结束
export const oortEndSubmit = (orderNo: string, submitType: 1 | 0, isSuccess: 1 | 0) => {
  trackEvent({
    PageName: '帮我找驾校页h5',
    actionName: '结束提交线索',
    actionType: '',
    orderNo,
    submitType,
    isSuccess
  });
};
