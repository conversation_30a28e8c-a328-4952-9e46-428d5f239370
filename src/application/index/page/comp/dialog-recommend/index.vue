<template>
  <div class="dialog-mask" v-if="visible">
    <div class="dialog-content">
      <h3>系统为您推荐</h3>
      <div>
        <div class="content-box">
          <div
            class="logo"
            :style="{
              background: 'url(' + (isJiaxiao ? recommendData.logo : recommendData.avatar) + ') no-repeat center',
              backgroundSize: 'cover'
            }"
          >
            <i
              :style="{
                background: 'url(' + recommendData.logoIcon + ')',
                backgroundSize: '100% auto'
              }"
            ></i>
          </div>
          <div class="right">
            <div class="jiaxiao">
              <span>{{ recommendData.name }}</span>
              <span v-if="!isJiaxiao" class="jiaxiao_name">{{ recommendData.jiaxiaoName }}</span>
              <div v-for="(item, index) in recommendData.privileges" :key="index">
                <i
                  :style="{
                    background: 'url(' + item + ') no-repeat center',
                    backgroundSize: '100% auto'
                  }"
                ></i>
              </div>
              <i class="vip vip icon_0" v-if="recommendData.vipLevel === 30">
                <span>{{ recommendData.vipYear }}年</span>
              </i>
              <i class="vip vip icon_30" v-if="recommendData.vipLevel === 50">
                <span>{{ recommendData.vipYear }}年</span>
              </i>
              <i class="vip vip icon_80" v-if="recommendData.vipLevel === 80">
                <span>{{ recommendData.vipYear }}年</span>
              </i>
              <i class="vip vip icon_100" v-if="recommendData.vipLevel === 100">
                <span>{{ recommendData.vipYear }}年</span>
              </i>
            </div>
            <div class="course">
              <div class="icon">
                <div class="hui"></div>
                <i :style="{ width: scoreWidth }"></i>
              </div>
              <span>{{ recommendData.score || 3 }}分</span>
            </div>
            <div class="rank_item">
              <div class="rank" v-if="recommendData.rank">
                {{ recommendData.rank }}
              </div>
              <div class="dianping">{{ recommendData.dianpingCount || 0 }}条评价</div>
            </div>
          </div>
        </div>
        <div class="distance">
          驾校距您{{ isJiaxiao ? recommendData.distanceDesc : recommendData.distanceText }}，{{
            recommendData.courseName
          }}
          <span v-if="recommendData.coursePrice">仅需{{ recommendData.coursePrice }}起</span>
          <span v-else>价格面议</span>
        </div>
      </div>
      <div class="btns-list">
        <div class="btn-list btn-cancel" @click="submitAll">不感兴趣</div>
        <div class="btn-list btn-submit" @click="submitOnly">咨询该{{ isJiaxiao ? '驾校' : '教练' }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
