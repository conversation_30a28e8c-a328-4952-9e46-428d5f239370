import { SourceEnum } from '@/application/index/page/comp/form/constants';
import { CoachItemModel, JiaxiaoItemModel, TargetModel } from '@/application/index/page/comp/form/types';

export interface StateModel {
  // 是否显示弹框
  visible: boolean;
  // 推荐数据
  recommendData: JiaxiaoItemModel | CoachItemModel;
  // 推荐类型
  recommendType: SourceEnum;
  // 评分样式宽度
  scoreWidth: string;
}

export type RecommendReturn =
  | {
      interested: true;
      targets: TargetModel[];
    }
  | {
      interested: false;
      targets: [];
    };
