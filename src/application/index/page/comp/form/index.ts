import MCProtocol from '@simplex/simple-mcprotocol';
import { computed, defineComponent, PropType, reactive, ref, toRefs } from 'vue';
import Utils from '@/utils/utils';
import UI from '@/utils/util-ui';
import useRouter from '@/utils/hooks/router';
import { REG_RULE } from '@/utils/constant';
import { ADDRESS, CAR_DESC, CAR_TYPE, DURATION } from '@/utils/storage-key';
import { GetSimpleAreaStore } from '@/store/volvo/lead';
import {
  BaseParamsModel,
  EmitModel,
  PostDataModel,
  SimpleAreaResponse,
  StateModel,
  UrlQueryModel
} from '@/application/index/page/comp/form/types';
import { QuickLoginResponse } from '@/application/index/page/types';
import { getSubmitMethod, saveErrorReq } from '@/application/index/page/comp/form/utils';
import { LoginResponse } from '@/application/index/page/comp/verify-phone-code/types';
import VerifyPhoneComp from '@/application/index/page/comp/verify-phone/index.vue';
import { ToastEnum } from '@/application/index/page/comp/form/constants';
import {
  oortAliyunPhoneCheck,
  oortEndSubmit,
  oortFormCheckSuccess,
  oortLoginUserSubmit,
  oortStartSubmit,
  oortSubmitButton,
  oortToast,
  oortUpdateLocation
} from '@/application/index/page/util-oort';
import { UAV_LICENSE_LIST } from '@/utils/options/license';

let authToken = '';
let mucangId = '';

export default defineComponent({
  components: {
    VerifyPhoneComp
  },
  emits: ['submitEnd'],
  props: {
    userCity: {
      type: String,
      required: true
    },
    urlQuery: {
      type: Object as PropType<UrlQueryModel>,
      default: (): UrlQueryModel => ({})
    },
    isBanLogin: {
      type: Boolean,
      required: true
    }
  },
  setup(props, { emit }: { emit: EmitModel }) {
    const router = useRouter();

    const state = reactive(<StateModel>{
      // 手机号码
      phone: '',
      // 您的称呼,
      name: '',
      // 是否触发过姓名input
      isNameClick: false,
      // 是否触发过手机号input
      isPhoneClick: false,
      // 学车地址
      address: {},
      // 驾照类型
      carType: '',
      carDesc: '',
      // 隐私政策
      protocolChoose: false,
      // 是否一键登录/验证码登录
      isLogin: false
    });

    const computeds = {
      carFull: computed(() => {
        // 无人机不展示type
        if (UAV_LICENSE_LIST.includes(state.carType)) {
          return state.carDesc;
        }

        return state.carType + ' ' + state.carDesc;
      })
    };

    const components = {
      verifyPhoneRef: ref<InstanceType<typeof VerifyPhoneComp>>(null)
    };

    const methods = {
      // 回填storage参数
      getStorage() {
        // Utils.store.clear();
        const carType = Utils.store.get(CAR_TYPE);
        const carDesc = Utils.store.get(CAR_DESC);
        state.carType = carType || state.carType;
        state.carDesc = carDesc || state.carDesc;

        const address = Utils.store.get(ADDRESS);
        state.address = address ? JSON.parse(address) : state.address;

        Utils.store.del(CAR_TYPE);
        Utils.store.del(CAR_DESC);
        Utils.store.del(ADDRESS);
      },
      // 清空手机号，恢复未登录状态
      clearPhone() {
        state.phone = '';
        state.isLogin = false;
      },
      // 跳转到学车地址，存储页面信息
      goMap() {
        router.goPathJumps({
          path: 'map.html',
          coreOpenParams: {
            titleBar: false,
            orientation: 'portrait',
            button: '',
            menu: false
          }
        });
      },
      // 跳转到驾照类型，存储页面信息
      goLicense() {
        Utils.store.set(CAR_TYPE, state.carType);
        router.goPathJumps({
          path: 'license.html',
          coreOpenParams: {
            titleBar: false,
            orientation: 'portrait',
            button: '',
            menu: false
          }
        });
      },
      // 跳转到隐私政策
      goPrivate() {
        MCProtocol.Core.Web.open({
          titleBar: true,
          title: '隐私政策',
          orientation: 'portrait',
          button: '',
          menu: false,
          url: 'https://laofuzi.kakamobi.com/agreements/privateAgreement.html'
        });
      },
      // 跳转到个人信息保护声明
      goProtect() {
        const cityCode = state.address.cityCode || props.userCity;
        MCProtocol.Core.Web.open({
          titleBar: true,
          title: '个人信息保护声明',
          orientation: 'portrait',
          button: '',
          menu: false,
          url: `https://share-m.kakamobi.com/activity.kakamobi.com/jiaxiaozhijia-information-protection/?addressCode=${cityCode}`
        });
      },
      // 表单校验
      checkForm() {
        if (!REG_RULE.phone.test(state.phone) && !state.isLogin) {
          UI.toast('请填写正确的手机号');
          oortToast(ToastEnum.PHONE);
          return false;
        }
        if (!REG_RULE.name.test(state.name)) {
          UI.toast('请输入中文姓名');
          oortToast(ToastEnum.NAME);
          return false;
        }
        if (!state.address.cityCode) {
          UI.toast('请选择学车地址');
          oortToast(ToastEnum.ADDRESS);
          return false;
        }
        if (!state.carType) {
          UI.toast('请选择驾照类型');
          return false;
        }
        if (!state.protocolChoose) {
          UI.toast('您需要同意隐私政策和个人信息保护声明才能提交');
          oortToast(ToastEnum.PROTOCOL);
          return false;
        }

        return true;
      },
      // 提交
      onSubmit: Utils.debounce(async () => {
        oortSubmitButton(props.urlQuery.entrance, state.isNameClick && state.name !== '' ? 1 : 0);

        // 表单校验
        if (!methods.checkForm()) {
          return;
        }
        console.log('form check end');

        oortFormCheckSuccess(props.urlQuery.entrance);

        console.log('isLogin: ', state.isLogin);
        // 如果没登录，弹出验证码登录
        if (!state.isLogin) {
          await methods.onSubmitAuth();
        }

        // 获取请求的基础参数
        const baseParams = await methods.getParams();

        // 留资
        methods.fetchClue(baseParams);

        // 留资结束
        emit('submitEnd', baseParams);
      }),
      // 阿里云校验当前手机号成功直接登录，否则走验证码逻辑，需要打开数据流量
      onSubmitAuth(): Promise<void> {
        return new Promise(resolve => {
          console.log('start Core.User.numAuth');
          MCProtocol.Core.User.numAuth({
            phone: state.phone,
            callback: async res => {
              console.log('MCProtocol.Core.User.numAuth res: ', res);
              if (res.data === 'PASS') {
                oortAliyunPhoneCheck(1);
                oortLoginUserSubmit(props.urlQuery.entrance);
                resolve();
              } else {
                oortAliyunPhoneCheck(2);
                const codeData: LoginResponse = await components.verifyPhoneRef.value.open(state.phone);

                authToken = codeData.authToken;
                mucangId = codeData.mucangId;
                state.isLogin = true;

                resolve();
              }
            }
          });
        });
      },
      // 留资
      fetchClue(params: BaseParamsModel): Promise<void> {
        return new Promise((resolve, reject) => {
          const submitMethodInfo = getSubmitMethod(params);

          let postData: PostDataModel;
          if (!submitMethodInfo.isStepSubmit) {
            postData = params;
            params.targets = JSON.stringify(params.targets);
            // 重新生成订单号
            params.orderNo = Utils.getOrderNo();
          } else {
            // 分步提交提前通知客户端-orderNo
            Utils.isAndroid &&
              Utils.partSendOrderNo({
                orderNo: params.orderNo,
                callback: save => {
                  // 提前通知客户端-orderNo成功
                  oortUpdateLocation(params.orderNo, save && save.success ? 1 : 0);
                }
              });
            postData = {
              content: JSON.stringify(params),
              finished: false,
              type: 1
            };
          }

          oortStartSubmit(params.orderNo, submitMethodInfo.isStepSubmit ? 1 : 0);
          UI.loading();
          console.log('留资参数：');
          console.log(postData);
          console.log('authToken: ', authToken, 'state.isLogin: ', state.isLogin);

          submitMethodInfo
            .fn(postData)
            .then((res: { result: boolean }) => {
              oortEndSubmit(params.orderNo, submitMethodInfo.isStepSubmit ? 1 : 0, res.result ? 1 : 0);
              if (res.result) {
                MCProtocol.jxxy.enquiry.finished();
              } else {
                UI.toast('网络异常');
              }
              resolve();
            })
            .catch(err => {
              oortEndSubmit(params.orderNo, submitMethodInfo.isStepSubmit ? 1 : 0, 0);
              Utils.isAndroid &&
                Utils.partSendOrderNo({
                  orderNo: '',
                  callback: save => {
                    oortUpdateLocation('', save && save.success ? 1 : 0);
                  }
                });
              saveErrorReq(submitMethodInfo.url, postData);
              UI.toast(err.message);
              reject();
            })
            .finally(() => {
              UI.loadingHide();
            });
        });
      },
      inputPhone() {
        state.isNameClick = true;
      },
      inputName() {
        state.isPhoneClick = true;
      },
      // 一键登录回填用户信息
      setUserInfo(res: QuickLoginResponse) {
        if (res.success) {
          state.phone = res.data.phone;
          // 一半的概率回显“驾考学员”，防止默认用户名看起来太假的问题
          state.name = Math.random() < 0.5 ? res.data.nickname : '驾考学员';
          authToken = res.data.authToken;
          mucangId = res.data.mucangId;
          state.isLogin = true;
        } else {
          state.isLogin = false;
        }
      },
      // 获取基础参数
      getParams(): Promise<BaseParamsModel> {
        return new Promise(resolve => {
          Promise.all([
            Utils.postAccuracyKey(),
            // 防止cityCode不一致
            GetSimpleAreaStore({
              adcode: state.address.cityCode,
              province: state.address.location.province,
              city: state.address.location.city,
              district: state.address.location.district
            }).catch(() => ({ simpleCode: state.address.cityCode }))
          ]).then(([accuracyKey, simpleArea]) => {
            const { accuracy } = accuracyKey;
            const { simpleCode } = simpleArea as SimpleAreaResponse;

            state.address.cityCode = simpleCode || state.address.cityCode;
            const durationEnd = parseInt(Utils.store.get(DURATION));
            const meta = {
              Duration: new Date().getTime() - durationEnd
            };
            const baseParams: BaseParamsModel = {
              orderNo: Utils.getOrderNo(),
              userName: state.name || '驾考用户',
              userPhone: state.phone,
              userLongitude: state.address.location.lng,
              userLatitude: state.address.location.lat,
              userLicenseType: state.carType,
              userId: mucangId,
              userAddress: state.address.name,
              targets: [
                {
                  // *询价的教练or驾校id， 如果targetType=0，则targetId也为0
                  targetId: props.urlQuery.jiaxiaoId ? Number(props.urlQuery.jiaxiaoId) : 0,
                  //*0=非定向 2=教练 3=驾校
                  targetType: props.urlQuery.jiaxiaoId ? 3 : 0
                }
              ],
              submitPoint: 0,
              entrancePage1: Utils.setEntrancePage({
                isBanLogin: props.isBanLogin
              }),
              entrancePage2: props.urlQuery.entrance === 'newuser' ? 'new-user-process-66' : 'homepage-entrance-66',
              tokenLead: state.isLogin,
              accuracy: accuracy,
              userAreaCode: state.address.cityCode,
              listSource: 'default',
              meta: JSON.stringify(meta),
              clientCreateTime: new Date().getTime()
            };

            resolve(baseParams);
          });
        });
      }
    };

    return {
      ...toRefs(state),
      ...computeds,
      ...components,
      ...methods
    };
  }
});
