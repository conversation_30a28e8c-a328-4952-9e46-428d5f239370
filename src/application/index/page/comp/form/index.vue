<template>
  <div class="h-[100vh] pl-15 pr-15 bg-[rgba(0,136,250,1)]">
    <div class="bg-[rgba(255,255,255,1)] rounded-8 pt-10 pr-15 pb-25 pl-15 text-15">
      <ul>
        <li class="li flex items-center border-b-1 border-[rgba(238,238,238,1)] border-solid pt-15 pb-15">
          <div class="mr-15">手机号码</div>
          <input
            v-model="phone"
            :disabled="isLogin"
            type="tel"
            placeholder="请输入您的手机号码"
            maxlength="11"
            @input="inputPhone"
          />
          <div @click="clearPhone" class="icon-del"></div>
        </li>
        <li class="li flex items-center border-b-1 border-[rgba(238,238,238,1)] border-solid pt-15 pb-15">
          <div class="mr-15">您的称呼</div>
          <input v-model="name" type="text" placeholder="请输入您的称呼" maxlength="4" @input="inputName" />
          <div @click="name = ''" class="icon-del"></div>
        </li>
        <li
          class="li flex items-center border-b-1 border-[rgba(238,238,238,1)] border-solid pt-15 pb-15"
          @click="goMap"
        >
          <div class="mr-15">学车地址</div>
          <input v-model="address.name" type="text" placeholder="请选择您的学车地址" readonly />
          <div class="icon-arrow"></div>
        </li>
        <li
          class="li flex items-center border-b-1 border-[rgba(238,238,238,1)] border-solid pt-15 pb-15"
          @click="goLicense"
        >
          <div class="mr-15">驾照类型</div>
          <input v-model="carFull" type="text" placeholder="请选择您的驾照类型" readonly />
          <div class="icon-arrow"></div>
        </li>
      </ul>
      <div class="relative pl-20 mt-20 text-15">
        <i
          class="absolute -left-0 top-5 w-14 h-14 block"
          :class="[protocolChoose ? 'active' : 'no-active']"
          @click="protocolChoose = !protocolChoose"
        ></i>
        <span class="text-[rgba(102,102,102,1)]">同意</span>
        <a @click="goPrivate" class="text-[rgba(43,177,249,1)]">《隐私政策》</a>
        <a @click="goProtect" class="text-[rgba(43,177,249,1)]">《个人信息保护声明》</a>
        <span class="text-[rgba(43,177,249,1)]">及提交成功后将会有驾校联系你</span>
      </div>
      <div
        @click="onSubmit"
        class="w-full h-44 text-16 mt-12 text-[rgba(255,255,255,1)] bg-gradient-to-r from-[rgba(103,206,248,1)] to-[rgba(30,116,250,1)] rounded-22 leading-44 text-center"
      >
        免费咨询
      </div>
    </div>
  </div>
  <verify-phone-comp ref="verifyPhoneRef" :url-query="urlQuery" />
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
