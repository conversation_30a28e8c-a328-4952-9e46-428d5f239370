import { defineComponent, PropType, reactive, ref, toRefs } from 'vue';
import VerifyPhoneSliderComp from '@/application/index/page/comp/verify-phone-slider/index.vue';
import VerifyPhoneCodeComp from '@/application/index/page/comp/verify-phone-code/index.vue';
import Deferred, { DeferredModel } from '@/utils/deferred';
import { LoginResponse } from '@/application/index/page/comp/verify-phone-code/types';
import { UrlQueryModel } from '@/application/index/page/comp/form/types';

export default defineComponent({
  components: {
    VerifyPhoneSliderComp,
    VerifyPhoneCodeComp
  },
  props: {
    urlQuery: {
      type: Object as PropType<UrlQueryModel>,
      default: (): UrlQueryModel => ({})
    }
  },
  setup() {
    let dtd: DeferredModel<LoginResponse> = null;
    let currentPhone: string;

    const state = reactive({});

    const constants = {};

    const components = {
      sliderRef: ref<InstanceType<typeof VerifyPhoneSliderComp>>(null),
      codeRef: ref<InstanceType<typeof VerifyPhoneCodeComp>>(null)
    };

    const methods = {
      open(phone: string) {
        currentPhone = phone;
        components.sliderRef.value.show();

        dtd = Deferred();

        return dtd.promise;
      },
      onEnd(item: LoginResponse) {
        dtd.resolve(item);

        methods.close();
      },
      onSuccess(token: string) {
        components.codeRef.value.show(currentPhone, token);
      },
      onBack() {
        components.sliderRef.value.show();
      },
      close() {
        methods.reset();
      },
      reset() {
        dtd = null;
      }
    };

    return {
      ...toRefs(state),
      ...constants,
      ...components,
      ...methods
    };
  }
});
