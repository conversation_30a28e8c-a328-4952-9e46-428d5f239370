<template>
  <div class="dialog-mask" v-if="visible">
    <div class="dialog-content">
      <div class="code">
        <div class="icon"></div>
        <input class="input" v-model="code" type="tel" placeholder="请输入验证码" maxlength="6" />
        <div class="refresh">
          <span @click="onBack" v-if="countdown === 0">重新获取</span>
          <span v-else>{{ countdown + 's' }}</span>
        </div>
      </div>
      <div class="tips">提交成功后，推荐的驾校或教练将会与你联系</div>
      <div @click="onSubmit" class="btn">提交</div>
    </div>
    <div @click="onClose" class="dialog-close"></div>
  </div>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
