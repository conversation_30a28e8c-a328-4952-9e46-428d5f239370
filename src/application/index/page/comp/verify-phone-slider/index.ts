import { defineComponent, reactive, toRefs } from 'vue';
import { EmitModel, StateModel } from './types';

export default defineComponent({
  emits: ['success'],
  setup(props, { emit }: { emit: EmitModel }) {
    const state = reactive(<StateModel>{
      visible: false // 是否显示网易图形验证码
    });

    const methods = {
      // 显示网易图形验证码
      show() {
        state.visible = true;
        (window as any).initNECaptcha(
          {
            captchaId: '6f92317b6e7d4f4faa77a360d65826c5',
            element: '#captcha',
            mode: 'embed',
            width: '100%',
            onVerify(err, data) {
              if (!err && data !== undefined) {
                state.visible = false;

                emit('success', data.validate);
              }
            }
          },
          function onload(instance) {
            // 初始化成功
            console.log('126code onload: ', instance);
          },
          function onerror(err) {
            // 验证码初始化失败处理逻辑，例如：提示用户点击按钮重新初始化
            console.log('126code onerror: ', err);
            state.visible = false;
          }
        );
      },

      // 初始化插入js
      init() {
        const script = document.createElement('script');
        script.src = 'https://cstaticdun.126.net/load.min.js?t=201903281201';
        document.head.appendChild(script);
      }
    };

    methods.init();

    return {
      ...toRefs(state),
      ...methods
    };
  }
});
