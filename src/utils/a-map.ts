import AMapLoader from '@amap/amap-jsapi-loader';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
window._AMapSecurityConfig = {
  serviceHost: 'https://amap-proxy.mucang.cn/_AMapService'
};

const AMapInit = (plugins: string[] = []) => {
  return AMapLoader.load({
    key: 'b051b06edda886b9377fbba47a85c6f0',
    version: '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
    plugins,
    AMapUI: {
      version: '1.1'
    }
  });
};

export default AMapInit;
