import MCProtocol from '@simplex/simple-mcprotocol';
import md5 from '@/initialize/md5';
import {
  ACCESS_FINE_LOCATION_STATUS,
  OLDAPPFUNS,
  EnterEnquiryCodeEnum,
  ACCESS_FINE_LOCATION,
  ENTRANCE
} from '@/utils/constant';
import { HANDLE_CHOOSE_LAT_BOOL, IOS_ENTER_FIRST } from '@/utils/storage-key';
import { SystemInfoModel } from '@/utils/types';

import { LICENSE_OPTIONS } from '@/utils/options/license';

const UASTR = window.navigator.userAgent || '';
// 买车宝典快应用 userAgent (如下) 内含有mucang，特殊处理下
// Mozilla/5.0 (Linux; Android 12; LE2100 Build/RKQ1.211119.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/101.0.4951.61 Mobile Safari/537.36 hap/*******/oneplus com.nearme.instant.platform/com.nearme.instant.platform com.mucang.maiche/2.2.0 ({"packageName":"com.nearme.instant.platform","type":"unknown","extra":{}})

export default {
  isLocal: APP.isLocal,
  isTest: APP.isTest,
  isInApp:
    !!(UASTR.toLowerCase().match(/mucang/g) || window.MCProtocolIosExecute || window.getMucangIOSWebViewData) &&
    !UASTR.toLowerCase().match(/\.mucang\./g),
  isAndroid: !!UASTR.match(/android/gi),
  isIOS: !!UASTR.match(/(iPhone|iPad|iPod|iOS)/gi),

  debounce(func, delay = 200) {
    let timer: NodeJS.Timeout;
    return function (this, ...args) {
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const context = this;
      clearTimeout(timer);
      timer = setTimeout(() => {
        func.apply(context, args);
      }, delay);
    };
  },

  getURLParams(filter: string, str?: string): Record<string, any> {
    const params = {};
    let filterRegx;

    if (str) {
      str = str.match(/\?/gi) ? str : '?' + str;
    } else {
      str = window.location.href;
    }

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    str.replace(/[#|?&]+([^=#|&]+)=([^#|&]*)/gi, function (m: string, key: string, value: string) {
      filterRegx = new RegExp(filter, 'gi');

      params[key] = decodeURIComponent(value);

      if (filter && !key.match(filterRegx)) {
        delete params[key];
      }
    });

    return params;
  },

  getRandomR(a) {
    const c = Math.abs(parseInt(String(new Date().getTime() * Math.random() * 10000))).toString();
    let d = 0;
    let b;

    for (b = 0; b < c.length; b++) {
      d += parseInt(c[b]);
    }
    const e = (function (f) {
      return function (g, h) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        return h - '' + g.length <= 0 ? g : (f[h] || (f[h] = Array(h + 1).join(0))) + g;
      };
    })([]);
    d += c.length;
    d = e(d, 3 - d.toString().length);

    return a.toString() + c + d;
  },

  store: {
    get(key) {
      let value = '';
      try {
        // eslint-disable-next-line no-restricted-syntax
        value = localStorage.getItem(key || '');
      } catch (e) {
        // eslint-disable-next-line no-console
        // console.log(e);
      }
      return value;
    },
    set(key, value) {
      try {
        // eslint-disable-next-line no-restricted-syntax
        localStorage.setItem(key, value || '');
      } catch (e) {
        // eslint-disable-next-line no-console
        // console.log(e);
      }
    },
    del(key) {
      try {
        // eslint-disable-next-line no-restricted-syntax
        localStorage.removeItem(key);
      } catch (e) {
        // eslint-disable-next-line no-console
        // console.log(e);
      }
    },
    clear() {
      try {
        // eslint-disable-next-line no-restricted-syntax
        localStorage.clear();
      } catch (e) {
        // eslint-disable-next-line no-console
        // console.log(e);
      }
    }
  },

  hasMCFeature(xieyi) {
    let status = false;

    let xieyiBefore = [];
    let xieyiAray = [];
    let x1;
    let x2;
    let x3;

    if (!xieyi) {
      return console.error('协议不能为空');
    }

    if (this.isInApp) {
      xieyi = xieyi.toLowerCase();
      xieyiBefore = JSON.parse(JSON.stringify((window.mucang && window.mucang.features) || []));

      // features有数据，按照features来检测；features没数据，或不存在，按照数组 OLDAPPFUNS 来检测
      if (xieyiBefore.length) {
        xieyiBefore.forEach(function (item, idx) {
          xieyiBefore[idx] = (item || '').toLowerCase();
        });

        if (xieyi.match(/./gi)) {
          xieyiAray = xieyi.split('.');
          x1 = xieyiAray[0] || '';
          x2 = xieyiAray[1] || '';
          x3 = xieyiAray[2] || '';

          xieyi = x1 + '.luban.mucang.cn/' + x2;

          if (x3) {
            xieyi += '/' + x3;
          }
        }

        if (xieyiBefore.indexOf(xieyi) !== -1) {
          status = true;
        }
      } else if (OLDAPPFUNS.indexOf(xieyi) !== -1) {
        status = true;
      }
    }

    if (status) {
      // console.log('协议支持检测-' + xieyi + '-结果：', status);
    } else {
      // console.warn('协议支持检测-' + xieyi + '-结果：', status);
    }

    return status;
  },

  split(text: string, lineSplitter: string, fieldSplitter: string, trim: boolean) {
    const list = {};

    let parts;
    let keyName;
    let arr;
    let keyValue;

    let pairs;
    let i;
    let len;

    if (text) {
      lineSplitter = lineSplitter || '\n';
      fieldSplitter = fieldSplitter || '\t';

      pairs = text.split(lineSplitter);

      for (i = 0, len = pairs.length; i < len; i++) {
        parts = pairs[i].split(fieldSplitter);
        keyName = parts[0];
        keyValue = decodeURIComponent(parts[1]);

        if (trim) {
          keyName = keyName.trim();
        }

        if (typeof list[keyName] === 'undefined') {
          list[keyName] = keyValue;
        } else if (typeof list[keyName] === 'string') {
          arr = [list[keyName], keyValue];
          list[keyName] = arr;
        } else {
          list[keyName].push(keyValue);
        }
      }
    }

    return list;
  },

  param(a, traditional = '') {
    const buildParams = function (prefix, obj, traditional, add) {
      let name;
      const rbracket = /\[]$/;

      if (Array.isArray(obj)) {
        obj.forEach(function (v, i) {
          if (traditional || rbracket.test(prefix)) {
            add(prefix, v);
          } else {
            buildParams(prefix + '[' + (typeof v === 'object' ? i : '') + ']', v, traditional, add);
          }
        });
      } else if (!traditional && typeof obj === 'object') {
        for (name in obj) {
          buildParams(prefix + '[' + name + ']', obj[name], traditional, add);
        }
      } else {
        add(prefix, obj);
      }
    };

    let prefix;
    const s = [];

    const add = function (key, value) {
      key = encodeURIComponent(key);

      if (Object.prototype.toString.call(value) === '[object Function]') {
        value = value();
      } else if (value === null) {
        value = '';
      }

      if (Array.isArray(value)) {
        value = value[0];
        value.concat().forEach(function (v) {
          value = '&' + key + '=' + encodeURIComponent(v);
        });
      } else {
        value = encodeURIComponent(value);
      }

      s[s.length] = key + '=' + value;
    };

    if (Array.isArray(a)) {
      a.forEach(function (v) {
        add(v.name, v.value);
      });
    } else {
      for (prefix in a) {
        buildParams(prefix, a[prefix], traditional, add);
      }
    }

    return s.join('&');
  },

  // 回填驾照类型，安卓取不到该协议
  getIosCarType(cb) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    MCProtocol['jiakao-global'].getStatistics({
      callback: res => {
        console.log('jiakao-global.getStatistics: ', res);
        if (res.success) {
          LICENSE_OPTIONS.forEach(item => {
            if (item.groupType === res.data.carStyle) {
              cb(item.list[0]);
            }
          });
        }
      }
    });
  },
  // 只会触发一次，清空缓存之后才能再次触发
  updateFirstEnquiry(cb) {
    console.log('enterFirstEnquiryH5 start');
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    MCProtocol.jxxy.enterFirstEnquiryH5({
      callback: res => {
        console.log('enterFirstEnquiryH5: ', res);
        if (res.success) {
          this.store.set(IOS_ENTER_FIRST, EnterEnquiryCodeEnum.IOS);
        }
        cb && cb();
      }
    });

    // 本地模拟预加载完成
    if (this.isLocal || this.isTest) {
      this.store.set(IOS_ENTER_FIRST, EnterEnquiryCodeEnum.IOS);
      cb && cb();
    }
  },

  getOrderNo() {
    return md5(+new Date() + Math.random().toString().substr(2, 10) + Math.random().toString().substr(2, 9));
  },

  // 获取系统基础参数
  getSystemBaseInfo(): Promise<SystemInfoModel['data']> {
    return new Promise(resolve => {
      // 1、返回的数据格式为 {data: {}}
      // 2、另外安卓有白名单限制，也就是本地地址会取不到正确数据，只有线上地址才能拿到app内数据
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      MCProtocol.Core.System.info((res: SystemInfoModel) => {
        // console.log(res);
        // 本地模拟数据
        // res.data._userCity = '430000';
        // res.data._longitude = 116.39;
        // res.data._latitude = 39.9;
        resolve(res.data);
      });
    });
  },

  // 监听 page visibility
  listenPage(cb) {
    let hidden;
    let visibilityChange;
    if (typeof document.hidden !== 'undefined') {
      hidden = 'hidden';
      // 标准
      visibilityChange = 'visibilitychange';
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
    } else if (typeof document.webkitHidden !== 'undefined') {
      // Chrome
      hidden = 'webkitHidden';
      visibilityChange = 'webkitvisibilitychange';
    }
    // 添加监听器
    document.addEventListener(
      visibilityChange,
      () => {
        // console.log('当前页面是否被隐藏：' + document[hidden]);
        cb && cb(document[hidden]);
      },
      false
    );
  },
  // 获取新版定位信息
  getPositionerInfo() {
    return new Promise((resolve, reject) => {
      MCProtocol.Core.System.locationInfo({
        callback: data => {
          // console.log(data);
          if (data.success) {
            const locationInfo = typeof data.data === 'string' ? JSON.parse(data.data) : data.data;
            // console.log(locationInfo, 'locationInfo');
            resolve(locationInfo);
          } else {
            reject();
          }
        }
      });
    });
  },
  // 检查是否有精准定位权限
  checkAccurateControl(): Promise<any> {
    return new Promise((resolve, reject) => {
      MCProtocol.permission.permission.check({
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        config: { permission: ACCESS_FINE_LOCATION },
        callback: res => {
          if (res.message) {
            resolve(res);
          } else {
            reject();
          }
        }
      });
    });
  },
  // 获取新版定位信息
  postAccuracyKey(): Promise<any> {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async resolve => {
      if (this.isAndroid) {
        const hasPosition = await this.checkAccurateControl();
        // 开启了精准定位
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        const bool = hasPosition.message === ACCESS_FINE_LOCATION_STATUS.success;
        // 是否手动选择了地址
        const getHandleChooseLatBool = this.store.get(HANDLE_CHOOSE_LAT_BOOL);
        const { _accuracy } = await this.getPositionerInfo();
        if (!bool && !getHandleChooseLatBool) {
          resolve({
            accuracy: _accuracy
          });
        } else {
          resolve({
            accuracy: 0
          });
        }
      } else {
        resolve({
          accuracy: 0
        });
      }
    });
  },
  // 对象转url字符串参数
  objToParams(obj) {
    const params = [];
    for (const o in obj) {
      params.push(o + '=' + encodeURIComponent(obj[o]));
    }
    return params.join('&');
  },
  // 拨打电话
  callPhone(tex, callMessage) {
    MCProtocol.Core.System.call({
      title: tex,
      phone: callMessage
    });
  },
  // 分布提交数据协议调用
  partSendOrderNo(config) {
    MCProtocol.jxxy.enquiry.saveOrderNo({
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      orderNo: config.orderNo,
      callback: config.callback
    });
  },
  // 拼接字符串参数
  setEntrancePage(obj) {
    return ENTRANCE.reduce(function (pre, item) {
      if (obj[item.key]) {
        return pre + item.value;
      } else {
        return pre;
      }
    }, 'helpMeFindingSchool_h5');
  }
};
