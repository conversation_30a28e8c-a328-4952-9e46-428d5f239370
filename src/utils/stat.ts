/*
 * ------------------------------------------------------------------
 * 打点相关
 * ------------------------------------------------------------------
 */

import OORT from '@simplex/simple-oort';
import { StatPropsModel } from '@/utils/types';

/** 打点初始化 */
export function init() {
  OORT.init({});
}

/** 通用打点方法 */
export function trackEvent(props: StatPropsModel) {
  const {
    PageName,
    eventId = 'jiaxiao202101',
    fragmentName1 = '',
    fragmentName2 = '',
    actionType = '',
    actionName = '',
    numbers = {},
    ...otherProps
  } = props;
  const data = {
    // 处理通用参数
    strs: {
      ...otherProps
    },
    numbers
  };

  const eventName = [fragmentName1, fragmentName2, actionType + actionName].filter(Boolean).join('_');

  OORT.logEvt(PageName, eventName, eventId, data);
}
