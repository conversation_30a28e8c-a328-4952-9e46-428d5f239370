import { AppJoinItemModel, GroupItemModel } from '@/utils/types/license';

// 机动车驾照类型
export const LICENSE_OPTIONS: GroupItemModel[] = [
  {
    groupName: '小车',
    groupType: 'car',
    list: [
      {
        carType: 'C1',
        carDesc: '小型汽车'
      },
      {
        carType: 'C2',
        carDesc: '小型自动挡汽车'
      }
    ]
  },
  {
    groupName: '摩托车',
    groupType: 'moto',
    list: [
      {
        carType: 'D',
        carDesc: '普通三轮摩托车'
      },
      {
        carType: 'E',
        carDesc: '普通二轮摩托车'
      },
      {
        carType: 'F',
        carDesc: '轻便摩托车'
      }
    ]
  },
  {
    groupName: '货车',
    groupType: 'truck',
    list: [
      {
        carType: 'A2',
        carDesc: '牵引车'
      },
      {
        carType: 'B2',
        carDesc: '大型货车'
      }
    ]
  },
  {
    groupName: '客车',
    groupType: 'bus',
    list: [
      {
        carType: 'A1',
        carDesc: '大型客车'
      },
      {
        carType: 'A3',
        carDesc: '城市公交车'
      },
      {
        carType: 'B1',
        carDesc: '中型客车'
      }
    ]
  },

  {
    groupName: '其他',
    groupType: 'light_trailer',
    list: [
      {
        carType: 'C6',
        carDesc: '轻型牵引挂车'
      }
    ]
  }
];

// 无人机驾照类型
export const UAV_LICENSE_OPTIONS: GroupItemModel[] = [
  {
    groupName: '多旋翼无人机',
    groupType: 'UA_M',
    list: [
      {
        carType: 'UA_M__W',
        carDesc: '视距内驾驶员',
        tag: '大多数选择'
      },
      {
        carType: 'UA_M__B',
        carDesc: '超视距驾驶员'
      },
      {
        carType: 'UA_M__C',
        carDesc: '教员'
      }
    ]
  },
  {
    groupName: '固定翼无人机',
    groupType: 'UA_F',
    list: [
      {
        carType: 'UA_F__W',
        carDesc: '视距内驾驶员'
      },
      {
        carType: 'UA_F__B',
        carDesc: '超视距驾驶员'
      },
      {
        carType: 'UA_F__C',
        carDesc: '教员'
      }
    ]
  },
  {
    groupName: '无人直升机',
    groupType: 'UA_H',
    list: [
      {
        carType: 'UA_H__W',
        carDesc: '视距内驾驶员'
      },
      {
        carType: 'UA_H__B',
        carDesc: '超视距驾驶员'
      },
      {
        carType: 'UA_H__C',
        carDesc: '教员'
      }
    ]
  },
  {
    groupName: '垂直起降无人机',
    groupType: 'UA_V',
    list: [
      {
        carType: 'UA_V__W',
        carDesc: '视距内驾驶员'
      },
      {
        carType: 'UA_V__B',
        carDesc: '超视距驾驶员'
      },
      {
        carType: 'UA_V__C',
        carDesc: '教员'
      }
    ]
  }
];

// app 可能传入的驾照类型
export const CAR_TYPE_OPTIONS: AppJoinItemModel[] = [
  {
    carType: 'C1',
    carDesc: '小型汽车',
    defaultType: 'car'
  },
  {
    carType: 'D',
    carDesc: ' 摩托车',
    defaultType: 'moto'
  },
  {
    carType: 'A2',
    carDesc: '货车',
    defaultType: 'truck'
  },
  {
    carType: 'A1',
    carDesc: '客车',
    defaultType: 'bus'
  },
  {
    carType: 'C6',
    carDesc: '轻型牵引挂车',
    defaultType: 'light_trailer'
  }
];

// app 可能传入的无人机驾照类型
export const UAV_TYPE_OPTIONS: AppJoinItemModel[] = [
  {
    carType: 'UA_M__W',
    carDesc: '多旋翼无人机（视距内驾驶员）',
    defaultType: 'multi_wvr'
  },
  {
    carType: 'UA_M__B',
    carDesc: '多旋翼无人机（超视距驾驶员）',
    defaultType: 'multi_bvr'
  },
  {
    carType: 'UA_M__C',
    carDesc: '多旋翼无人机（教员）',
    defaultType: 'multi_coach'
  },
  {
    carType: 'UA_F__W',
    carDesc: '固定翼无人机（视距内驾驶员）',
    defaultType: 'fixed_wvr'
  },
  {
    carType: 'UA_F__B',
    carDesc: '固定翼无人机（超视距驾驶员）',
    defaultType: 'fixed_bvr'
  },
  {
    carType: 'UA_F__C',
    carDesc: '固定翼无人机（教员）',
    defaultType: 'fixed_coach'
  },
  {
    carType: 'UA_H__W',
    carDesc: '无人直升机（视距内驾驶员）',
    defaultType: 'helicopter_wvr'
  },
  {
    carType: 'UA_H__B',
    carDesc: '无人直升机（超视距驾驶员）',
    defaultType: 'helicopter_bvr'
  },
  {
    carType: 'UA_H__C',
    carDesc: '无人直升机（教员）',
    defaultType: 'helicopter_coach'
  },
  {
    carType: 'UA_V__W',
    carDesc: '垂直起降无人机（视距内驾驶员）',
    defaultType: 'vtol_wvr'
  },
  {
    carType: 'UA_V__B',
    carDesc: '垂直起降无人机（超视距驾驶员）',
    defaultType: 'vtol_bvr'
  },
  {
    carType: 'UA_V__C',
    carDesc: '垂直起降无人机（教员）',
    defaultType: 'vtol_coach'
  }
];

// 无人机驾照类型
export const UAV_LICENSE_LIST = [
  // 实际上报的驾照类型
  ...UAV_TYPE_OPTIONS.map(item => item.carType),
  // 客户端传过来的驾照类型
  ...UAV_TYPE_OPTIONS.map(item => item.defaultType)
];
