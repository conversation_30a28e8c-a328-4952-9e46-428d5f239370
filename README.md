```mermaid
flowchart TB
    A[点击提交按钮] --> B[表单校验]
    B --> C{阿里云手机号校验}
    C -->|校验成功| D[弹出确认弹层]
    C -->|校验失败| E[弹出手机号码弹层]
    D --> F{弹出确认弹层}
    F -->|命中| G[弹出确认弹层]
    F -->|未命中| H[获取 UserAreaCode]
    G --> I[abort moto_guide（停止引导配置）]
I --> H
H --> J[setTimeout 2000ms]
J --> K[获取 showNewNotify 设置为弹层弹层样式]
J --> L[获取 showToast1st 按钮常驻标识内容]
K --> M{有内容}
L --> M
M -->|命中| N[将引导弹层置为样式 K]
M -->|未命中| O[将引导弹层置为样式 2C]
N --> P[获取校验结果，设置页面标题为引用: singleRiskXku&risk]
O --> P
P --> Q[展示推荐列表]
P --> R[留言]
P --> S[留言]
Q --> T{判断推荐类型}
T --> U[recommendType === 'phone']
T --> V[其他类型: recommendType === 'chooseRecommend']
U --> W[拨打电话]
V --> X{是否是 RmkXku&risk === 0 且 手机号校验未通过且配置拨打组件}
X -->|是| Y[打开弹层停留，并显示手机号码信息弹层的组件]
X -->|否则| Z[关闭推荐弹层]
W --> AA[点击兴趣]
Y --> AA
Z --> AB[点击不感兴趣]
AB --> AC{判断是否需要二次确认}
AC -->|需要| AD[打开二次确认弹层]
AC -->|不需要| AE[setPageGoto]
AD --> AF[点击取消]
AD --> AG[点击确定]
AF --> AE
AG --> AE
AA --> AE
AE --> AH{searchInformation === '1'}
AH -->|是| AI[跳转: 传 ApplicationName 参数]
AH -->|否则| AJ[获取 abtestRecommendShowConfig 仅对弹层埋点参数]
AJ --> AK{是否是一个埋点}
AK -->|是| AL[打开弹层]
AK -->|不是| AM[关闭流程]
AL --> AN[点击弹层关闭按钮]
AN --> AO[关闭弹层]
AE --> AP[点击立即拨打]
AE --> AQ[点击暂不拨打]
AP --> AR[调用拨打组件拨打电活]
AQ --> AS[关闭相关引导弹层]
```